data:
  tokenizer: null 
  train_files: ~/verl/tests/e2e/arithmetic_sequence/data/train.parquet
  val_files: ~/verl/tests/e2e/arithmetic_sequence/data/test.parquet
  prompt_key: prompt
  max_prompt_length: 16
  max_response_length: 32
  train_batch_size: 800
  val_batch_size: 200
  return_raw_input_ids: True  # This should be set to true when the tokenizer between policy and rm differs
  return_raw_chat: False

actor_rollout_ref:
  hybrid_engine: True
  model:
    path: ~/verl/tests/e2e/arithmetic_sequence/model
    tokenizer_path: ${actor_rollout_ref.model.path}
    external_lib: tests.e2e.envs.digit_completion
    override_config: {}
    enable_gradient_checkpointing: False
    use_remove_padding: False
  actor:
    strategy: fsdp  # This is for backward-compatibility
    ppo_mini_batch_size: 200
    ppo_micro_batch_size: 200
    use_dynamic_bsz: False
    ppo_max_token_len_per_gpu: 16384 # n * ${data.max_prompt_length} + ${data.max_response_length}
    grad_clip: 1.0
    clip_ratio: 0.2
    entropy_coeff: 0.0
    use_kl_loss: False # True for GRPO
    kl_loss_coef: 0.001 # for grpo
    kl_loss_type: low_var_kl # for grpo
    ppo_epochs: 1
    shuffle: False
    ulysses_sequence_parallel_size: 1 # sp size
    optim:
      lr: 1e-4
    fsdp_config:
      wrap_policy:
        # transformer_layer_cls_to_wrap: None
        min_num_params: 0
      param_offload: False
      grad_offload: False
      optimizer_offload: False
  ref:
    fsdp_config:
      param_offload: False
      wrap_policy:
        # transformer_layer_cls_to_wrap: None
        min_num_params: 0
    micro_batch_size: 200
    log_prob_use_dynamic_bsz: ${actor_rollout_ref.actor.use_dynamic_bsz}
    log_prob_max_token_len_per_gpu: ${actor_rollout_ref.actor.ppo_max_token_len_per_gpu}
    ulysses_sequence_parallel_size: ${actor_rollout_ref.actor.ulysses_sequence_parallel_size} # sp size
  rollout:
    name: hf
    temperature: 1.0
    top_k: -1 # 0 for hf rollout, -1 for vllm rollout
    top_p: 1
    prompt_length: ${data.max_prompt_length}  # for xperf_gpt
    response_length: ${data.max_response_length}
    # for vllm rollout
    dtype: bfloat16 # should align with FSDP
    gpu_memory_utilization: 0.1
    ignore_eos: False
    micro_batch_size: 200
    enforce_eager: True
    free_cache_engine: True
    load_format: dummy_dtensor
    tensor_model_parallel_size: 1
    max_num_batched_tokens: 8192
    max_num_seqs: 1024
    log_prob_micro_batch_size: 200
    log_prob_use_dynamic_bsz: ${actor_rollout_ref.actor.use_dynamic_bsz}
    log_prob_max_token_len_per_gpu: ${actor_rollout_ref.actor.ppo_max_token_len_per_gpu}
    # for hf rollout
    do_sample: True
    # number of responses (i.e. num sample times)
    n: 1 # > 1 for grpo

critic:
  strategy: fsdp
  optim:
    lr: 1e-3
  model:
    path: ~/verl/tests/e2e/arithmetic_sequence/model
    tokenizer_path: ${actor_rollout_ref.model.path}
    override_config: {}
    external_lib: ${actor_rollout_ref.model.external_lib}
    enable_gradient_checkpointing: False
    use_remove_padding: False
    fsdp_config:
      param_offload: False
      grad_offload: False
      optimizer_offload: False
      wrap_policy:
        # transformer_layer_cls_to_wrap: None
        min_num_params: 0
  ppo_mini_batch_size: ${actor_rollout_ref.actor.ppo_mini_batch_size}
  ppo_micro_batch_size: 200
  forward_micro_batch_size: ${critic.ppo_micro_batch_size}
  use_dynamic_bsz: ${actor_rollout_ref.actor.use_dynamic_bsz}
  ppo_max_token_len_per_gpu: 32768 # (${actor_rollout_ref.actor.ppo_max_token_len_per_gpu}) * 2
  forward_max_token_len_per_gpu: ${critic.ppo_max_token_len_per_gpu}
  ulysses_sequence_parallel_size: 1 # sp size
  ppo_epochs: ${actor_rollout_ref.actor.ppo_epochs}
  shuffle: ${actor_rollout_ref.actor.shuffle}
  grad_clip: 1.0
  cliprange_value: 0.5

  # the following parameters are for backward-compatibility and should be removed
  kl_ctrl:
    type: fixed
    kl_coef: 0.001

reward_model:
  strategy: fsdp
  enable: False
  model:
    input_tokenizer: ${actor_rollout_ref.model.path}  # set this to null if the chat template is identical
    path: ~/models/FsfairX-LLaMA3-RM-v0.1
    external_lib: ${actor_rollout_ref.model.external_lib}
    offload: False
    use_remove_padding: False
    fsdp_config:
      min_num_params: 0
  micro_batch_size: 8
  max_length: null
  ulysses_sequence_parallel_size: 1 # sp size

algorithm:
  gamma: 1.0
  lam: 1.0
  adv_estimator: gae
  kl_penalty: kl  # how to estimate kl divergence
  kl_ctrl:
    type: fixed
    kl_coef: 0.005

trainer:
  total_epochs: 200
  total_training_steps: null
  project_name: verl_examples
  experiment_name: arithmetic_sequences
  logger: ['console']
  nnodes: 1
  n_gpus_per_node: 1
  save_freq: -1
  test_freq: 1
  critic_warmup: 0
  default_hdfs_dir: ~/experiments/gsm8k/ppo/${trainer.experiment_name}
  default_local_dir: checkpoints/${trainer.project_name}/${trainer.experiment_name}
