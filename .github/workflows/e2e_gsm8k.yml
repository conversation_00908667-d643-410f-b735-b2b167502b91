name: e2e_gsm8k

on:
  # Trigger the workflow on push or pull request,
  # but only for the main branch
  push:
    branches:
      - main
    paths:
      - "**/*.py"
      - .github/workflows/e2e_gsm8k.yml
  pull_request:
    branches:
      - main
    paths:
      - "**/*.py"
      - .github/workflows/e2e_gsm8k.yml
      - "tests/e2e/*.sh"

jobs:
  e2e_gsm8k:
    runs-on: [self-hosted, l20-1]
    env:
      HTTP_PROXY: ${{ secrets.PROXY_HTTP }}
      HTTPS_PROXY: ${{ secrets.PROXY_HTTPS }}
      NO_PROXY: "localhost,127.0.0.1"
      HF_HUB_ENABLE_HF_TRANSFER: 1
    container:
      image: verlai/verl:vemlp-th2.4.0-cu124-vllm0.6.3-ray2.10-te1.7-v0.0.3
      options: --gpus all --shm-size=10g
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
            fetch-depth: 0
      - name: Install the current repository
        run: |
          pip3 install hf_transfer
          pip3 install -e .[test]
      - name: Prepare gsm8k dataset
        run: |
          ray stop --force
          python3 examples/data_preprocess/gsm8k.py
      - name: Running gsm8k e2e training tests on 8 L20 GPUs with rmpad using function rm
        run: |
          ray stop --force
          bash tests/e2e/run_qwen_gsm8k_function_rm.sh
      - name: Running gsm8k e2e without rmpad using function rm
        run: |
          ray stop --force
          bash tests/e2e/run_qwen_gsm8k_function_rm_no_rmpad.sh
      - name: Running gsm8k e2e with rmpad using model rm
        run: |
          ray stop --force
          bash tests/e2e/run_qwen_gsm8k_model_rm.sh
      - name: Running gsm8k e2e without rmpad using model rm
        run: |
          ray stop --force
          bash tests/e2e/run_qwen_gsm8k_model_rm_no_rmpad.sh
      - name: Running gsm8k e2e with rmpad using model rm and ulysses sp=2
        run: |
          ray stop --force
          bash tests/e2e/run_qwen_gsm8k_model_rm_ulysses.sh
      - name: Running gsm8k e2e with rmpad using model rm and dynamic batch size
        run: |
          ray stop --force
          bash tests/e2e/run_qwen_gsm8k_model_rm_seq_balance.sh
