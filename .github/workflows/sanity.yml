name: sanity

on:
  # Trigger the workflow on push or pull request,
  # but only for the main branch
  push:
    branches:
      - main
    paths:
      - "**/*.py"
      - .github/workflows/sanity.yml
  pull_request:
    branches:
      - main
    paths:
      - "**/*.py"
      - .github/workflows/sanity.yml

jobs:
  sanity:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.10"]
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@0b93645e9fea7318ecaed2b359559ac225c90a2b # v5.3.0
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install the current repository
        run: |
          pip install -e .[test]
      - name: Run sanity test
        run: |
          pytest -s -x tests/sanity
      - name: Run untility test
        run: |
          pytest -s -x tests/utility
      - name: Run license test
        run: |
          python3 tests/sanity/check_license.py --directory .
