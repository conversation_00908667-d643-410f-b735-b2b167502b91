#%% md
# Run Qwen PPO with [verl](https://github.com/volcengine/verl)

This tutorial provides a step-by-step guide to using veRL for executing your RLHF pipeline. You can find our [github repo](https://github.com/volcengine/verl/) and [documentation](https://verl.readthedocs.io/en/latest/index.html) for mode details.

This notebook is also published on the [Lightning Studio](https://lightning.ai/hlin-verl/studios/verl-getting-started) platform, which provides free GPU quota every month. Checkout the published notebook with pre-installed dependencies using a free L4 GPU [here](https://lightning.ai/hlin-verl/studios/verl-getting-started) (no credit card required).

### You will learn:

- How to install veRL from scratch.
- How to use existing scripts to run an RLHF pipeline with your own models and data.
#%% md
# Dependency Installation

If you are running on Lightning Studio using the published notebook, the dependencies are **already installed** and you can proceed to step "**Load Pretrained Language Model**"
#%%
!pip3 install --upgrade pip setuptools wheel
!pip3 install torch==2.4.0 torchvision==0.19.0
!pip3 list | grep torch
!pip3 install flash-attn --no-build-isolation
#%% md
## Install and verify verl
Now we're ready to install verl!
#%%
# In case you run this notebook and have not cloned verl yet:
# !git clone https://github.com/volcengine/verl $HOME/verl_repo

!cd $HOME/verl_repo && pip3 install -e . -U
#%% md
## Restart the python kernel
#%%
import IPython

# Restart the kernel to pickup the latest python packages
IPython.get_ipython().kernel.do_shutdown(restart=True)
#%%
import torch
try:
  assert torch.cuda.is_available() is True
  torch.ones(1, dtype=torch.bfloat16).cuda()
except AssertionError:
  print("Please switch to an env with GPUs supporting bfloat16 (L4 RTX 5000, A5000, A100, H100, A10, etc)")

try:
  import verl
except Exception as e:
  print("Please install verl via pip and restart the kernel")
  raise e

import flash_attn
#%% md
# Load Pretrained Language Model

verl supports models available in Huggingface transformers (as well as custom Megatron models).

Let's download the model first.
#%%
!huggingface-cli download Qwen/Qwen2.5-0.5B-Instruct --local-dir $HOME/models/Qwen2.5-0.5B-Instruct

# If huggingface-cli is not stable, use the method below
# import transformers
# transformers.pipeline('text-generation', model='Qwen/Qwen2.5-0.5B-Instruct')
#%% md
# Dataset preparation

We train with the Grade School Math 8K (GSM8k) task in this demo. The dataset is downloaded from huggingface [gsm8k](https://huggingface.co/datasets/openai/gsm8k) and below are some samples:


**Prompt**

Katy makes coffee using teaspoons of sugar and cups of water in the ratio of 7:13. If she used a total of 120 teaspoons of sugar and cups of water, calculate the number of teaspoonfuls of sugar she used.

**Solution**

The total ratio representing the ingredients she used to make the coffee is 7+13 = <<7+13=20>>20 Since the fraction representing the number of teaspoons she used is 7/20, she used 7/20120 = <<7/20120=42>>42 #### 42
#%%
!mkdir -p $HOME/data/gsm8k
!python3 $HOME/verl_repo/examples/data_preprocess/gsm8k.py --local_dir $HOME/data/gsm8k
#%% md
# the reward

We use a rule-based reward model. We force the model to produce a final answer following 4 `#` as shown in the solution. We extract the final answer from both the solution and model's output using regular expression matching. We compare them and assign a reward of 1 to correct answer, 0.1 to incorrect answer and 0 to no answer.
#%%
import inspect
from verl.utils.reward_score.gsm8k import compute_score as gsm8k_reward
print(inspect.getsource(gsm8k_reward))
#%% md
# Run the RL Pipeline
Let's start with the Proximal Policy Optimization (PPO) algorithm,  one of the most widely used methods for post-training large language models.

The main entry point of the PPO algorithm example is: `main_ppo.py`. A detailed guide to understanding the code architecture of `main_ppo.py` is available [here](https://verl.readthedocs.io/en/latest/examples/ppo_code_architecture.html).

In this tutorial, we will demonstrate how to run the PPO algorithm with **Qwen 2.5-0.5B** by setting:
- `trainer.n_gpus_per_node`: Number of GPUs per node.

- `actor_rollout_ref.rollout.tensor_model_parallel_size`: TP size for rollout. Only effective for vllm.

- `actor_rollout_ref/critic.model.path`: Huggingface model path. This can be either local path or HDFS path. For HDFS path, we provide utils to download it to DRAM and convert the HDFS path to local path.

- `data.train_batch_size`: Batch size sampled for one training iteration of different RL algorithms.

- `data.max_prompt_length`: Maximum prompt length. All prompts will be left-padded to this length. An error will be reported if the length is too long.

- `data.max_response_length`: Maximum response length. Rollout in RL algorithms (e.g. PPO) generates up to this length.

- `actor_rollout_ref.actor.ppo_mini_batch_size`: One sample is split into multiple sub-batches with batch_size=ppo_mini_batch_size for PPO updates.

- `actor_rollout_ref/critic.actor.ppo_micro_batch_size`: Similar to gradient accumulation, the micro_batch_size for one forward pass, trading speed for GPU memory.

The full configuration explanation is available [here](https://verl.readthedocs.io/en/latest/examples/config.html).

The training may take a few hours to finish but you can observe how the model performance increases. It will progressively output:

- generated sentences.

- step information with RL metrics, such as entropy loss, kl, and ``val/test_score/openai/gsm8k`` (validated every ``trainer.test_freq`` steps)

If you come across GPU out of memory issues, set smaller values for the micro batch size used for gradient accumulation:

- actor_rollout_ref.actor.ppo_micro_batch_size=1
- critic.ppo_micro_batch_size=1
#%%
!PYTHONUNBUFFERED=1 python3 -m verl.trainer.main_ppo \
 data.train_files=$HOME/data/gsm8k/train.parquet \
 data.val_files=$HOME/data/gsm8k/test.parquet \
 data.train_batch_size=256 \
 data.val_batch_size=1312 \
 data.max_prompt_length=512 \
 data.max_response_length=256 \
 actor_rollout_ref.model.path=$HOME/models/Qwen2.5-0.5B-Instruct \
 actor_rollout_ref.actor.optim.lr=1e-6 \
 actor_rollout_ref.actor.ppo_mini_batch_size=64 \
 actor_rollout_ref.actor.ppo_micro_batch_size=1 \
 actor_rollout_ref.rollout.log_prob_micro_batch_size=1 \
 actor_rollout_ref.rollout.tensor_model_parallel_size=1 \
 actor_rollout_ref.rollout.gpu_memory_utilization=0.4 \
 actor_rollout_ref.ref.log_prob_micro_batch_size=4 \
 critic.optim.lr=1e-5 \
 critic.model.path=$HOME/models/Qwen2.5-0.5B-Instruct \
 critic.ppo_micro_batch_size=1 \
 algorithm.kl_ctrl.kl_coef=0.001 \
 +trainer.val_before_train=False \
 trainer.default_hdfs_dir=null \
 trainer.n_gpus_per_node=1 \
 trainer.nnodes=1 \
 trainer.save_freq=10 \
 trainer.test_freq=10 \
 trainer.total_epochs=15 \
 trainer.logger=\[console\]
#%% md
# Stop and clean up resources
#%%
!ray stop
#%%
